{"swagger": "2.0", "info": {"title": "protos/area.proto", "version": "version not set"}, "tags": [{"name": "AreaService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/admin/area": {"get": {"operationId": "AreaService_getAreas", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcAreaResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["AreaService"]}}}, "definitions": {"grpcAreaResponse": {"type": "object", "properties": {"countries": {"type": "array", "items": {"type": "string"}}, "languages": {"type": "array", "items": {"type": "string"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}