package test

import "fmt"

const (
	// dev1 服务器 内网地址 (注意，在 k8s 上需映射)
	dev1_local_url_base = "http://192.168.1.200:15445"
	// dev2 服务器 内网地址 (注意，在 k8s 上需映射)
	dev2_local_url_base = "http://192.168.1.200:25906"
	// sit 服务器 内网地址
	sit_local_url_base = "http://192.168.1.200:30000"

	// dev1 服务器 外网地址
	dev1_external_url_base = "http://ny10wt9045294.vicp.fun:25639"
	// dev2 服务器 外网地址
	dev2_external_url_base = "http://ny10wt9045294.vicp.fun"
	// sit 服务器 外网地址
	sit_external_url_base = "http://ny10wt9045294.vicp.fun:29397"
)

func main() {
	run_env := "dev1"

	local_url_base := ""
	external_url_base := ""
	if run_env == "dev1" {
		local_url_base = dev1_local_url_base
		external_url_base = dev1_external_url_base

	} else if run_env == "dev2" {
		local_url_base = dev2_local_url_base
		external_url_base = dev2_external_url_base

	} else if run_env == "sit" {
		local_url_base = sit_local_url_base
		external_url_base = sit_external_url_base

	} else {
		fmt.Printf("Unsupported run_env:%v\n", run_env)
		return
	}

	StartTest(run_env, local_url_base, external_url_base)
}

func StartTest(run_env, local_url_base, external_url_base string) {

}
