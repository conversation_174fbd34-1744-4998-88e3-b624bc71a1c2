{"swagger": "2.0", "info": {"description": "支付后端服务API文档，提供订单管理、支付处理等功能", "title": "Payment Backend API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "wenchao", "url": "xxx", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1"}, "paths": {"/api/v1/pay-service/admin/orders": {"get": {"description": "----------------------------------------------------\n### 详细 curl 测试命令请见接口自测文档:  https://isrc.iscas.ac.cn/gitlab/aibook/platform/payment-service/-/blob/dev/payment-backend/docs/test_curl.md?ref_type=heads\n----------------------------------------------------", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单管理"], "summary": "获取所有订单列表", "parameters": [{"maximum": 500, "minimum": 1, "type": "integer", "default": 50, "description": "每页数量，默认50，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "偏移量，从0开始", "name": "offset", "in": "query"}, {"type": "string", "description": "用户ID过滤", "name": "user_id", "in": "query"}, {"type": "string", "description": "货币代码过滤，ISO-4217 币种, 如USD、CNY等", "name": "currency", "in": "query"}, {"type": "string", "description": "支付状态过滤，如 'created'(订单创建),'paid'(支付中),'succeeded'(支付成功),'failed'(支付失败),'expired'(支付过期),'cancelled'(取消支付) 等", "name": "pay_status", "in": "query"}, {"type": "string", "description": "支付方式过滤，待定", "name": "payed_method", "in": "query"}, {"type": "string", "description": "支付服务提供商过滤，如stripe、paypal 等", "name": "psp_provider", "in": "query"}, {"type": "string", "description": "支付时间开始，RFC3339格式，如2023-01-01T00:00:00Z", "name": "payed_at_start", "in": "query"}, {"type": "string", "description": "支付时间结束，RFC3339格式，如2023-12-31T23:59:59Z", "name": "payed_at_end", "in": "query"}, {"type": "string", "description": "退款状态过滤，如 'none'(未申请退款),'requested'(发起申请退款),'succeeded'(退款成功),'failed'(退款失败) 等", "name": "refund_status", "in": "query"}, {"type": "string", "description": "退款时间开始，RFC3339格式", "name": "refunded_at_start", "in": "query"}, {"type": "string", "description": "退款时间结束，RFC3339格式", "name": "refunded_at_end", "in": "query"}, {"type": "string", "description": "PSP价格ID过滤", "name": "psp_price_id", "in": "query"}, {"type": "string", "description": "PSP客户邮箱过滤", "name": "psp_customer_email", "in": "query"}, {"type": "string", "description": "PSP订阅ID过滤", "name": "psp_subscription_id", "in": "query"}], "responses": {"200": {"description": "成功返回订单列表", "schema": {"$ref": "#/definitions/domain.ListOrdersResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}, "/api/v1/pay-service/admin/orders/:order_id/force-refund": {"post": {"description": "管理员强制退款，不论订单是否已支付，都可以退款", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单管理"], "summary": "管理员强制订单退款", "parameters": [{"type": "string", "description": "订单ID", "name": "order_id", "in": "path", "required": true}, {"description": "退款请求", "name": "request", "in": "body", "schema": {"$ref": "#/definitions/domain.RefundOrderRequest"}}], "responses": {"200": {"description": "成功退款", "schema": {"$ref": "#/definitions/domain.SuccessResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}, "/api/v1/pay-service/admin/store-service/packages": {"get": {"description": "获取所有流量包列表，包含完整信息，需要管理员权限", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["流量包管理-管理员"], "summary": "获取所有流量包（管理员）", "parameters": [{"type": "string", "example": "\"USD\"", "description": "货币单位", "name": "currency", "in": "query"}, {"type": "string", "example": "\"US\"", "description": "国家", "name": "country", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "default": 50, "description": "每页数量，最小1，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "偏移量，从0开始", "name": "offset", "in": "query"}], "responses": {"200": {"description": "流量包列表", "schema": {"$ref": "#/definitions/store.AdminListPackagesResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}, "put": {"description": "添加新的流量包，需要管理员权限。entitlement为int32类型，discount_price为优惠价格，支持优惠有效期设置，sale_status控制起售/停售状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["流量包管理-管理员"], "summary": "添加流量包", "parameters": [{"description": "创建流量包请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store.CreatePackageRequest"}}], "responses": {"201": {"description": "创建成功", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}, "post": {"description": "更新指定的流量包信息，需要管理员权限", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["流量包管理-管理员"], "summary": "更新流量包", "parameters": [{"description": "更新流量包请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store.UpdatePackageRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}, "delete": {"description": "删除指定的流量包，需要管理员权限", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["流量包管理-管理员"], "summary": "删除流量包", "parameters": [{"description": "删除流量包请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store.DeletePackageRequest"}}], "responses": {"200": {"description": "删除成功", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}, "/api/v1/pay-service/order-service/orders": {"get": {"description": "----------------------------------------------------\n### 详细 curl 测试命令请见接口自测文档:  https://isrc.iscas.ac.cn/gitlab/aibook/platform/payment-service/-/blob/dev/payment-backend/docs/test_curl.md?ref_type=heads#4-%E8%8E%B7%E5%8F%96%E7%94%A8%E6%88%B7%E8%AE%A2%E5%8D%95%E7%BB%88%E7%AB%AF%E8%B0%83%E7%94%A8-%E5%A4%96%E7%BD%91\n----------------------------------------------------", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单管理"], "summary": "获取用户订单列表", "parameters": [{"maximum": 500, "minimum": 1, "type": "integer", "default": 50, "description": "每页数量，默认50，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "偏移量，从0开始", "name": "offset", "in": "query"}, {"type": "string", "description": "货币代码过滤，ISO-4217 币种, 如USD、CNY等", "name": "currency", "in": "query"}, {"type": "string", "description": "支付状态过滤，如 'created'(订单创建),'paid'(支付中),'succeeded'(支付成功),'failed'(支付失败),'expired'(支付过期),'cancelled'(取消支付) 等", "name": "pay_status", "in": "query"}, {"type": "string", "description": "支付方式过滤，待定", "name": "payed_method", "in": "query"}, {"type": "string", "description": "支付服务提供商过滤，如stripe、paypal 等", "name": "psp_provider", "in": "query"}, {"type": "string", "description": "支付时间开始，RFC3339格式，如2023-01-01T00:00:00Z", "name": "payed_at_start", "in": "query"}, {"type": "string", "description": "支付时间结束，RFC3339格式，如2023-12-31T23:59:59Z", "name": "payed_at_end", "in": "query"}, {"type": "string", "description": "退款状态过滤，如 'none'(未申请退款),'requested'(发起申请退款),'succeeded'(退款成功),'failed'(退款失败) 等", "name": "refund_status", "in": "query"}, {"type": "string", "description": "退款时间开始，RFC3339格式", "name": "refunded_at_start", "in": "query"}, {"type": "string", "description": "退款时间结束，RFC3339格式", "name": "refunded_at_end", "in": "query"}, {"type": "string", "description": "PSP价格ID过滤", "name": "psp_price_id", "in": "query"}, {"type": "string", "description": "PSP客户邮箱过滤", "name": "psp_customer_email", "in": "query"}, {"type": "string", "description": "PSP订阅ID过滤", "name": "psp_subscription_id", "in": "query"}], "responses": {"200": {"description": "成功返回订单列表", "schema": {"$ref": "#/definitions/domain.ListOrdersResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}, "post": {"description": "创建新的订单并生成支付链接，需要用户认证", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单管理"], "summary": "创建订单", "parameters": [{"type": "string", "example": "\"user123\"", "description": "用户ID", "name": "x-user-id", "in": "header", "required": true}, {"type": "string", "example": "\"customer\"", "description": "用户角色", "name": "x-role", "in": "header", "required": true}, {"description": "创建订单请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateOrderRequest"}}], "responses": {"303": {"description": "订单创建成功，返回支付链接", "schema": {"$ref": "#/definitions/domain.CreateOrderResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}, "/api/v1/pay-service/store-service/packages": {"get": {"description": "获取所有可用的流量包列表，需要用户认证。返回结果包含优惠有效期信息，entitlement为int32类型，只显示起售状态的流量包", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["流量包管理"], "summary": "获取所有流量包", "parameters": [{"type": "string", "example": "\"user123\"", "description": "用户ID", "name": "x-user-id", "in": "header", "required": true}, {"type": "string", "example": "\"customer\"", "description": "用户角色", "name": "x-role", "in": "header", "required": true}, {"type": "string", "example": "\"USD\"", "description": "货币单位", "name": "currency", "in": "query"}, {"type": "string", "example": "\"US\"", "description": "国家", "name": "country", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "default": 50, "description": "每页数量，最小1，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "偏移量，从0开始", "name": "offset", "in": "query"}], "responses": {"200": {"description": "流量包列表", "schema": {"$ref": "#/definitions/store.ListPackagesResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}}, "definitions": {"domain.CreateOrderRequest": {"type": "object", "required": ["price_id", "product_id", "psp_provider", "quantity"], "properties": {"currency": {"type": "string", "example": "USD"}, "payed_method": {"type": "string", "example": "stripe"}, "price_id": {"type": "string", "example": "price_456"}, "product_desc": {"type": "string", "example": "Premium Subscription"}, "product_id": {"type": "string", "example": "prod_123"}, "psp_provider": {"type": "string", "example": "stripe"}, "quantity": {"type": "integer", "minimum": 1, "example": 1}}}, "domain.CreateOrderResponse": {"type": "object", "properties": {"amount": {"type": "number", "example": 99.99}, "checkout_url": {"type": "string", "example": "https://mock-payment.example.com/stripe/checkout/..."}, "currency": {"type": "string", "example": "USD"}, "expires_at": {"type": "string", "example": "2025-07-11T15:30:45Z"}, "order_id": {"type": "string", "example": "20250710153045999stripe1234567890123456789"}}}, "domain.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "example": "Missing required field: product_id"}}}, "domain.ListOrdersResponse": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/definitions/domain.Order"}}, "pagination": {"$ref": "#/definitions/domain.PaginationResponse"}}}, "domain.Order": {"type": "object", "properties": {"amount": {"type": "number"}, "card_number": {"type": "string"}, "created_at": {"type": "string"}, "currency": {"type": "string"}, "deleted": {"type": "boolean"}, "deleted_at": {"type": "string"}, "id": {"type": "integer"}, "net_amount": {"type": "number"}, "order_id": {"type": "string"}, "pay_ret": {"type": "string"}, "pay_status": {"type": "string"}, "payed_at": {"type": "string"}, "payed_method": {"type": "string"}, "price_id": {"type": "string"}, "product_desc": {"type": "string"}, "product_id": {"type": "string"}, "psp_customer_email": {"type": "string"}, "psp_customer_id": {"type": "string"}, "psp_payment_id": {"type": "string"}, "psp_payment_intent_id": {"type": "string"}, "psp_payment_refund_id": {"type": "string"}, "psp_payment_refund_ret": {"type": "string"}, "psp_price_id": {"type": "string"}, "psp_product_desc": {"type": "string"}, "psp_product_id": {"type": "string"}, "psp_provider": {"type": "string"}, "psp_subscription_id": {"type": "string"}, "quantity": {"type": "integer"}, "refund_status": {"type": "string"}, "refunded_at": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}}}, "domain.PaginationResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "remaining": {"type": "integer"}, "total": {"type": "integer"}}}, "domain.RefundOrderRequest": {"type": "object", "properties": {"amount": {"type": "number"}}}, "domain.SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "example": "Missing required field: product_id"}}}, "store.AdminListPackagesResponse": {"type": "object", "properties": {"packages": {"type": "array", "items": {"$ref": "#/definitions/store.AdminPackageResponse"}}, "pagination": {"$ref": "#/definitions/store.PaginationResponse"}}}, "store.AdminPackageResponse": {"type": "object", "properties": {"country": {"type": "string"}, "created_at": {"type": "string"}, "currency": {"type": "string"}, "discount_end_time": {"type": "string"}, "discount_price": {"type": "number"}, "discount_start_time": {"type": "string"}, "entitlement": {"type": "integer"}, "extra1": {"type": "string"}, "extra2": {"type": "string"}, "extra3": {"type": "integer"}, "extra4": {"type": "integer"}, "id": {"type": "integer"}, "original_price": {"type": "number"}, "package_desc": {"type": "string"}, "package_id": {"type": "string"}, "package_name": {"type": "string"}, "sale_status": {"type": "string"}, "updated_at": {"type": "string"}}}, "store.CreatePackageRequest": {"type": "object", "required": ["entitlement", "original_price", "package_desc", "package_name"], "properties": {"country": {"type": "string"}, "currency": {"type": "string"}, "discount_end_time": {"type": "string"}, "discount_price": {"type": "number"}, "discount_start_time": {"type": "string"}, "entitlement": {"type": "integer"}, "extra1": {"type": "string"}, "extra2": {"type": "string"}, "extra3": {"type": "integer"}, "extra4": {"type": "integer"}, "original_price": {"type": "number"}, "package_desc": {"type": "string"}, "package_name": {"type": "string"}, "sale_status": {"type": "string"}}}, "store.DeletePackageRequest": {"type": "object", "required": ["package_id"], "properties": {"package_id": {"type": "string"}}}, "store.ListPackagesResponse": {"type": "object", "properties": {"packages": {"type": "array", "items": {"$ref": "#/definitions/store.PackageResponse"}}, "pagination": {"$ref": "#/definitions/store.PaginationResponse"}}}, "store.PackageResponse": {"type": "object", "properties": {"country": {"type": "string"}, "currency": {"type": "string"}, "discount_end_time": {"description": "优惠结束时间", "type": "string"}, "discount_start_time": {"description": "优惠开始时间", "type": "string"}, "entitlement": {"type": "integer"}, "original_price": {"description": "原价（仅在有优惠时显示）", "type": "number"}, "package_desc": {"type": "string"}, "package_id": {"type": "string"}, "package_name": {"type": "string"}, "price": {"description": "实际价格（优惠价或原价）", "type": "number"}, "sale_status": {"description": "出售状态. on_sale(起售), off_sale(停售)", "type": "string"}}}, "store.PaginationResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "remaining": {"type": "integer"}, "total": {"type": "integer"}}}, "store.UpdatePackageRequest": {"type": "object", "required": ["package_id"], "properties": {"country": {"type": "string"}, "currency": {"type": "string"}, "discount_end_time": {"type": "string"}, "discount_price": {"type": "number"}, "discount_start_time": {"type": "string"}, "entitlement": {"type": "integer"}, "extra1": {"type": "string"}, "extra2": {"type": "string"}, "extra3": {"type": "integer"}, "extra4": {"type": "integer"}, "original_price": {"type": "number"}, "package_desc": {"type": "string"}, "package_id": {"type": "string"}, "package_name": {"type": "string"}, "sale_status": {"type": "string"}}}}}